#!/bin/bash

# Multi-LiDAR Calibrator Installation Verification Script
# This script verifies that all dependencies are installed and the package can be built

echo "=== Multi-LiDAR Calibrator Installation Verification ==="
echo

# Check ROS installation
echo "1. Checking ROS installation..."
if [ -z "$ROS_DISTRO" ]; then
    echo "   ❌ ROS not sourced. Please run: source /opt/ros/<distro>/setup.bash"
    exit 1
else
    echo "   ✅ ROS $ROS_DISTRO detected"
fi

# Check required ROS packages
echo "2. Checking ROS dependencies..."
required_packages=(
    "roscpp"
    "sensor_msgs" 
    "geometry_msgs"
    "std_srvs"
    "tf2"
    "tf2_ros"
    "pcl_ros"
    "pcl_conversions"
)

for package in "${required_packages[@]}"; do
    if rospack find $package >/dev/null 2>&1; then
        echo "   ✅ $package found"
    else
        echo "   ❌ $package not found"
        echo "      Install with: sudo apt-get install ros-$ROS_DISTRO-${package//_/-}"
    fi
done

# Check PCL installation
echo "3. Checking PCL installation..."
if pkg-config --exists pcl_common-1.10 || pkg-config --exists pcl_common-1.8; then
    echo "   ✅ PCL found"
else
    echo "   ❌ PCL not found"
    echo "      Install with: sudo apt-get install libpcl-dev"
fi

# Check catkin workspace
echo "4. Checking catkin workspace..."
if [ -f "CMakeLists.txt" ] && [ -f "package.xml" ]; then
    echo "   ✅ Package files found"
else
    echo "   ❌ Package files missing"
    exit 1
fi

# Try to build
echo "5. Testing compilation..."
if command -v catkin_make >/dev/null 2>&1; then
    echo "   Building package..."
    cd ..
    if catkin_make; then
        echo "   ✅ Package built successfully"
    else
        echo "   ❌ Build failed"
        exit 1
    fi
else
    echo "   ⚠️  catkin_make not found - manual verification needed"
fi

echo
echo "=== Verification Complete ==="
echo "If all checks passed, you can run:"
echo "  roslaunch multi_lidar_calibrator auto.launch"
echo
