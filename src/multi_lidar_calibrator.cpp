/**
 * @file multi_lidar_calibrator.cpp
 * @brief Multi-LiDAR extrinsic calibration using GICP for targetless calibration
 * <AUTHOR> Calibrator Developer
 * @date 2024
 */

#include <ros/ros.h>
#include <ros/master.h>
#include <sensor_msgs/PointCloud2.h>
#include <geometry_msgs/TransformStamped.h>
#include <std_srvs/Trigger.h>
#include <tf2_ros/static_transform_broadcaster.h>
#include <tf2/LinearMath/Quaternion.h>
#include <tf2/LinearMath/Matrix3x3.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/registration/gicp.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl_ros/point_cloud.h>

#include <mutex>
#include <map>
#include <string>
#include <vector>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <memory>

/**
 * @brief Structure to hold LiDAR frame data with timestamp
 */
struct LidarFrame {
    pcl::PointCloud<pcl::PointXYZ>::Ptr cloud;
    ros::Time timestamp;
    std::string frame_id;

    LidarFrame() : cloud(new pcl::PointCloud<pcl::PointXYZ>) {}
};

/**
 * @brief Structure to hold calibration transform result
 */
struct CalibrationResult {
    Eigen::Matrix4f transform;
    bool converged;
    double fitness_score;
    std::string parent_frame;
    std::string child_frame;

    CalibrationResult() : transform(Eigen::Matrix4f::Identity()),
                         converged(false), fitness_score(0.0) {}
};

/**
 * @brief Multi-LiDAR Calibrator Class
 */
class MultiLidarCalibrator {
private:
    ros::NodeHandle nh_;
    ros::NodeHandle pnh_;

    // Parameters
    std::string base_topic_;
    std::string base_frame_;
    bool auto_discover_;
    std::vector<std::string> other_topics_;
    double time_tolerance_;
    double voxel_leaf_;
    double gicp_max_corr_dist_;
    int gicp_max_iter_;
    bool continuous_refine_;
    bool publish_tf_static_;
    std::string save_yaml_path_;

    // Subscribers and Publishers
    ros::Subscriber base_sub_;
    std::map<std::string, ros::Subscriber> other_subs_;
    std::map<std::string, ros::Publisher> calibrated_pubs_;
    ros::ServiceServer save_service_;

    // TF broadcaster
    std::unique_ptr<tf2_ros::StaticTransformBroadcaster> static_tf_broadcaster_;

    // Data storage
    std::mutex data_mutex_;
    LidarFrame latest_base_frame_;
    std::map<std::string, LidarFrame> latest_other_frames_;
    std::map<std::string, CalibrationResult> calibration_results_;

    // PCL objects
    pcl::VoxelGrid<pcl::PointXYZ> voxel_filter_;
    std::map<std::string, pcl::GeneralizedIterativeClosestPoint<pcl::PointXYZ, pcl::PointXYZ>> gicp_map_;

public:
    /**
     * @brief Constructor
     */
    MultiLidarCalibrator() : nh_(), pnh_("~") {
        loadParameters();
        initializeComponents();
        setupSubscribersAndPublishers();

        ROS_INFO("Multi-LiDAR Calibrator initialized successfully");
        ROS_INFO("Base topic: %s", base_topic_.c_str());
        ROS_INFO("Auto discover: %s", auto_discover_ ? "true" : "false");
        if (!other_topics_.empty()) {
            ROS_INFO("Other topics specified: %zu topics", other_topics_.size());
        }
    }

private:
    /**
     * @brief Load ROS parameters
     */
    void loadParameters() {
        pnh_.param<std::string>("base_topic", base_topic_, "/lidar_base/points");
        pnh_.param<std::string>("base_frame", base_frame_, "");
        pnh_.param<bool>("auto_discover", auto_discover_, true);
        pnh_.param<double>("time_tolerance", time_tolerance_, 0.05);
        pnh_.param<double>("voxel_leaf", voxel_leaf_, 0.1);
        pnh_.param<double>("gicp_max_corr_dist", gicp_max_corr_dist_, 1.0);
        pnh_.param<int>("gicp_max_iter", gicp_max_iter_, 40);
        pnh_.param<bool>("continuous_refine", continuous_refine_, false);
        pnh_.param<bool>("publish_tf_static", publish_tf_static_, true);
        pnh_.param<std::string>("save_yaml_path", save_yaml_path_, "~/.ros/multi_lidar_extrinsics.yaml");

        // Parse topics parameter (space-separated)
        std::string topics_str;
        pnh_.param<std::string>("topics", topics_str, "");
        if (!topics_str.empty()) {
            std::istringstream iss(topics_str);
            std::string topic;
            while (iss >> topic) {
                other_topics_.push_back(topic);
            }
        }

        // Expand tilde in save path
        if (save_yaml_path_[0] == '~') {
            save_yaml_path_ = std::string(getenv("HOME")) + save_yaml_path_.substr(1);
        }
    }

    /**
     * @brief Initialize PCL components
     */
    void initializeComponents() {
        // Initialize voxel filter
        voxel_filter_.setLeafSize(voxel_leaf_, voxel_leaf_, voxel_leaf_);

        // Initialize static TF broadcaster
        if (publish_tf_static_) {
            static_tf_broadcaster_ = std::make_unique<tf2_ros::StaticTransformBroadcaster>();
        }
    }

    /**
     * @brief Setup subscribers and publishers
     */
    void setupSubscribersAndPublishers() {
        // Subscribe to base LiDAR
        base_sub_ = nh_.subscribe(base_topic_, 1, &MultiLidarCalibrator::baseCallback, this);

        // Setup other LiDAR topics
        if (auto_discover_ && other_topics_.empty()) {
            discoverLidarTopics();
        }

        // Subscribe to other LiDAR topics
        for (const auto& topic : other_topics_) {
            other_subs_[topic] = nh_.subscribe(topic, 1,
                boost::bind(&MultiLidarCalibrator::otherCallback, this, _1, topic));

            // Create publisher for calibrated point cloud
            std::string pub_topic = "/calibrated" + sanitizeTopicName(topic);
            calibrated_pubs_[topic] = nh_.advertise<sensor_msgs::PointCloud2>(pub_topic, 1);

            ROS_INFO("Subscribed to: %s, publishing calibrated to: %s", topic.c_str(), pub_topic.c_str());
        }

        // Setup save service
        save_service_ = nh_.advertiseService("/multi_lidar_calibrator/save_extrinsics",
                                           &MultiLidarCalibrator::saveExtrinsicsCallback, this);
    }

    /**
     * @brief Discover LiDAR topics automatically
     */
    void discoverLidarTopics() {
        ros::master::V_TopicInfo topic_infos;
        ros::master::getTopics(topic_infos);

        for (const auto& topic_info : topic_infos) {
            if (topic_info.datatype == "sensor_msgs/PointCloud2" &&
                topic_info.name != base_topic_) {
                other_topics_.push_back(topic_info.name);
                ROS_INFO("Auto-discovered LiDAR topic: %s", topic_info.name.c_str());
            }
        }

        ROS_INFO("Auto-discovered %zu additional LiDAR topics", other_topics_.size());
    }

    /**
     * @brief Sanitize topic name for publisher
     */
    std::string sanitizeTopicName(const std::string& topic) {
        std::string sanitized = topic;
        std::replace(sanitized.begin(), sanitized.end(), '/', '_');
        return sanitized;
    }

    /**
     * @brief Base LiDAR callback
     */
    void baseCallback(const sensor_msgs::PointCloud2::ConstPtr& msg) {
        std::lock_guard<std::mutex> lock(data_mutex_);

        // Convert to PCL
        pcl::fromROSMsg(*msg, *latest_base_frame_.cloud);
        latest_base_frame_.timestamp = msg->header.stamp;
        latest_base_frame_.frame_id = msg->header.frame_id;

        // Set base frame if not specified
        if (base_frame_.empty()) {
            base_frame_ = msg->header.frame_id;
        }

        // Process calibration with available other frames
        processCalibration();
    }

    /**
     * @brief Other LiDAR callback
     */
    void otherCallback(const sensor_msgs::PointCloud2::ConstPtr& msg, const std::string& topic) {
        std::lock_guard<std::mutex> lock(data_mutex_);

        // Convert to PCL
        pcl::fromROSMsg(*msg, *latest_other_frames_[topic].cloud);
        latest_other_frames_[topic].timestamp = msg->header.stamp;
        latest_other_frames_[topic].frame_id = msg->header.frame_id;
    }

    /**
     * @brief Process calibration for all available frame pairs
     */
    void processCalibration() {
        if (latest_base_frame_.cloud->empty()) {
            return;
        }

        for (const auto& topic : other_topics_) {
            auto it = latest_other_frames_.find(topic);
            if (it == latest_other_frames_.end() || it->second.cloud->empty()) {
                continue;
            }

            // Check time synchronization
            double time_diff = std::abs((latest_base_frame_.timestamp - it->second.timestamp).toSec());
            if (time_diff > time_tolerance_) {
                continue;
            }

            // Perform calibration
            performGICPCalibration(topic, it->second);
        }
    }

    /**
     * @brief Perform GICP calibration between base and other LiDAR
     */
    void performGICPCalibration(const std::string& topic, const LidarFrame& other_frame) {
        try {
            // Downsample point clouds
            pcl::PointCloud<pcl::PointXYZ>::Ptr base_filtered(new pcl::PointCloud<pcl::PointXYZ>);
            pcl::PointCloud<pcl::PointXYZ>::Ptr other_filtered(new pcl::PointCloud<pcl::PointXYZ>);

            voxel_filter_.setInputCloud(latest_base_frame_.cloud);
            voxel_filter_.filter(*base_filtered);

            voxel_filter_.setInputCloud(other_frame.cloud);
            voxel_filter_.filter(*other_filtered);

            if (base_filtered->empty() || other_filtered->empty()) {
                ROS_WARN("Empty point clouds after filtering for topic: %s", topic.c_str());
                return;
            }

            // Initialize or get GICP instance
            if (gicp_map_.find(topic) == gicp_map_.end()) {
                gicp_map_[topic].setMaxCorrespondenceDistance(gicp_max_corr_dist_);
                gicp_map_[topic].setMaximumIterations(gicp_max_iter_);
                gicp_map_[topic].setTransformationEpsilon(1e-6);
                gicp_map_[topic].setEuclideanFitnessEpsilon(1e-6);
            }

            auto& gicp = gicp_map_[topic];
            gicp.setInputSource(other_filtered);
            gicp.setInputTarget(base_filtered);

            // Use previous result as initial guess if available and continuous refinement is enabled
            Eigen::Matrix4f initial_guess = Eigen::Matrix4f::Identity();
            if (continuous_refine_ && calibration_results_[topic].converged) {
                initial_guess = calibration_results_[topic].transform;
            }

            // Perform alignment
            pcl::PointCloud<pcl::PointXYZ> aligned;
            gicp.align(aligned, initial_guess);

            // Store result
            CalibrationResult& result = calibration_results_[topic];
            result.converged = gicp.hasConverged();
            result.transform = gicp.getFinalTransformation();
            result.fitness_score = gicp.getFitnessScore(gicp_max_corr_dist_);
            result.parent_frame = base_frame_;
            result.child_frame = other_frame.frame_id;

            if (result.converged) {
                ROS_INFO("GICP converged for %s: fitness = %.6f", topic.c_str(), result.fitness_score);

                // Publish static TF
                if (publish_tf_static_) {
                    publishStaticTF(result);
                }

                // Publish calibrated point cloud
                publishCalibratedPointCloud(topic, other_frame, result.transform);
            } else {
                ROS_WARN("GICP failed to converge for topic: %s", topic.c_str());
            }

        } catch (const std::exception& e) {
            ROS_ERROR("Exception in GICP calibration for %s: %s", topic.c_str(), e.what());
        }
    }

    /**
     * @brief Publish static TF transform
     */
    void publishStaticTF(const CalibrationResult& result) {
        geometry_msgs::TransformStamped transform_stamped;

        transform_stamped.header.stamp = ros::Time::now();
        transform_stamped.header.frame_id = result.parent_frame;
        transform_stamped.child_frame_id = result.child_frame;

        // Extract translation and rotation from transformation matrix
        Eigen::Vector3f translation = result.transform.block<3, 1>(0, 3);
        Eigen::Matrix3f rotation = result.transform.block<3, 3>(0, 0);

        transform_stamped.transform.translation.x = translation.x();
        transform_stamped.transform.translation.y = translation.y();
        transform_stamped.transform.translation.z = translation.z();

        // Convert rotation matrix to quaternion
        Eigen::Quaternionf quat(rotation);
        transform_stamped.transform.rotation.x = quat.x();
        transform_stamped.transform.rotation.y = quat.y();
        transform_stamped.transform.rotation.z = quat.z();
        transform_stamped.transform.rotation.w = quat.w();

        static_tf_broadcaster_->sendTransform(transform_stamped);
    }

    /**
     * @brief Publish calibrated point cloud
     */
    void publishCalibratedPointCloud(const std::string& topic, const LidarFrame& other_frame,
                                   const Eigen::Matrix4f& transform) {
        auto pub_it = calibrated_pubs_.find(topic);
        if (pub_it == calibrated_pubs_.end() || pub_it->second.getNumSubscribers() == 0) {
            return;
        }

        // Transform point cloud
        pcl::PointCloud<pcl::PointXYZ>::Ptr transformed_cloud(new pcl::PointCloud<pcl::PointXYZ>);
        pcl::transformPointCloud(*other_frame.cloud, *transformed_cloud, transform);

        // Convert to ROS message
        sensor_msgs::PointCloud2 output_msg;
        pcl::toROSMsg(*transformed_cloud, output_msg);
        output_msg.header.stamp = other_frame.timestamp;
        output_msg.header.frame_id = base_frame_;

        pub_it->second.publish(output_msg);
    }

    /**
     * @brief Save extrinsics service callback
     */
    bool saveExtrinsicsCallback(std_srvs::Trigger::Request& req, std_srvs::Trigger::Response& res) {
        try {
            std::ofstream file(save_yaml_path_);
            if (!file.is_open()) {
                res.success = false;
                res.message = "Failed to open file: " + save_yaml_path_;
                return true;
            }

            file << "# Multi-LiDAR Extrinsic Calibration Results\n";
            file << "# Generated by multi_lidar_calibrator\n";
            file << "transforms:\n";

            int saved_count = 0;
            for (const auto& pair : calibration_results_) {
                const auto& result = pair.second;
                if (!result.converged) {
                    continue;
                }

                // Extract translation and rotation
                Eigen::Vector3f translation = result.transform.block<3, 1>(0, 3);
                Eigen::Matrix3f rotation = result.transform.block<3, 3>(0, 0);
                Eigen::Quaternionf quat(rotation);

                file << "  - parent_frame: " << result.parent_frame << "\n";
                file << "    child_frame: " << result.child_frame << "\n";
                file << "    translation: {x: " << translation.x()
                     << ", y: " << translation.y()
                     << ", z: " << translation.z() << "}\n";
                file << "    rotation: {x: " << quat.x()
                     << ", y: " << quat.y()
                     << ", z: " << quat.z()
                     << ", w: " << quat.w() << "}\n";
                file << "    fitness_score: " << result.fitness_score << "\n";

                saved_count++;
            }

            file.close();

            res.success = true;
            res.message = "Successfully saved " + std::to_string(saved_count) +
                         " calibration results to " + save_yaml_path_;

            ROS_INFO("%s", res.message.c_str());
            return true;

        } catch (const std::exception& e) {
            res.success = false;
            res.message = "Exception while saving: " + std::string(e.what());
            ROS_ERROR("%s", res.message.c_str());
            return true;
        }
    }
};

/**
 * @brief Main function
 */
int main(int argc, char** argv) {
    ros::init(argc, argv, "multi_lidar_calibrator");

    try {
        MultiLidarCalibrator calibrator;
        ros::spin();
    } catch (const std::exception& e) {
        ROS_FATAL("Exception in main: %s", e.what());
        return 1;
    }

    return 0;
}
