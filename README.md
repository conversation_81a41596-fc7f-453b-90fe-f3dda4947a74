# Multi-LiDAR Calibrator

A ROS1 package for automatic extrinsic calibration of multiple LiDAR sensors using targetless GICP (Generalized Iterative Closest Point) registration.

## Features

- **Targetless Calibration**: No calibration targets required - uses natural scene features
- **Automatic Topic Discovery**: Automatically finds all LiDAR topics or use manually specified list
- **Real-time Processing**: Continuous calibration and point cloud fusion
- **GICP Registration**: Robust point cloud alignment using PCL's Generalized ICP
- **Static TF Publishing**: Publishes calibrated transforms for use by other nodes
- **YAML Export**: Save calibration results to file for persistence
- **Time Synchronization**: Handles multi-sensor temporal alignment
- **Configurable Parameters**: Extensive parameter tuning via ROS parameters

## Dependencies

### System Requirements
- Ubuntu 18.04/20.04 with ROS Melodic/Noetic
- C++17 compiler
- PCL 1.8+

### ROS Dependencies
```bash
sudo apt-get install ros-$ROS_DISTRO-pcl-ros ros-$ROS_DISTRO-pcl-conversions \
                     ros-$ROS_DISTRO-tf2 ros-$ROS_DISTRO-tf2-ros
```

### PCL Dependencies
```bash
sudo apt-get install libpcl-dev
```

## Installation

1. **Create/navigate to your catkin workspace:**
```bash
mkdir -p ~/catkin_ws/src
cd ~/catkin_ws/src
```

2. **Clone or copy this package:**
```bash
# If using git:
git clone <repository_url> multi_lidar_calibrator
# Or copy the package directory to src/
```

3. **Build the package:**
```bash
cd ~/catkin_ws
catkin_make
source devel/setup.bash
```

## Usage

### Quick Start

1. **Launch with auto-discovery (recommended):**
```bash
roslaunch multi_lidar_calibrator auto.launch base_topic:=/lidar_base/points
```

2. **Launch with specific topics:**
```bash
roslaunch multi_lidar_calibrator auto.launch \
    base_topic:=/lidar_base/points \
    auto_discover:=false \
    topics:="/lidar_front/points /lidar_left/points /lidar_right/points"
```

3. **Save calibration results:**
```bash
rosservice call /multi_lidar_calibrator/save_extrinsics
```

### Launch Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `base_topic` | `/lidar_base/points` | Base (reference) LiDAR topic |
| `base_frame` | `""` | Base frame (empty = use message frame_id) |
| `auto_discover` | `true` | Auto-discover LiDAR topics |
| `topics` | `""` | Space-separated other LiDAR topics |
| `time_tolerance` | `0.05` | Time sync tolerance (seconds) |
| `voxel_leaf` | `0.1` | Voxel filter leaf size (meters) |
| `gicp_max_corr_dist` | `1.0` | GICP max correspondence distance |
| `gicp_max_iter` | `40` | GICP maximum iterations |
| `continuous_refine` | `false` | Enable continuous refinement |
| `publish_tf_static` | `true` | Publish static TF transforms |
| `save_yaml_path` | `~/.ros/multi_lidar_extrinsics.yaml` | YAML save path |

### Output Topics

The calibrator publishes transformed point clouds to:
- `/calibrated/<sanitized_topic_name>` for each non-base LiDAR

Example: `/lidar_front/points` → `/calibrated/_lidar_front_points`

### Services

- `/multi_lidar_calibrator/save_extrinsics` (std_srvs/Trigger): Save calibration to YAML

## Configuration

### Parameter File
Edit `config/params.yaml` to modify default parameters:

```yaml
base_topic: "/lidar_base/points"
auto_discover: true
time_tolerance: 0.05
voxel_leaf: 0.1
gicp_max_corr_dist: 1.0
gicp_max_iter: 40
continuous_refine: false
publish_tf_static: true
save_yaml_path: "~/.ros/multi_lidar_extrinsics.yaml"
```

### Runtime Parameter Changes
```bash
rosparam set /multi_lidar_calibrator/continuous_refine true
rosparam set /multi_lidar_calibrator/voxel_leaf 0.05
```

## Output Format

### YAML Extrinsics File
```yaml
transforms:
  - parent_frame: lidar_base
    child_frame: lidar_front
    translation: {x: 0.50, y: 0.00, z: 0.10}
    rotation: {x: 0.01, y: -0.02, z: 0.05, w: 0.998}
    fitness_score: 0.023456
```

### Static TF
The calibrator publishes static transforms from each LiDAR frame to the base frame.

## Visualization

### RViz Setup
1. **Launch with RViz:**
```bash
roslaunch multi_lidar_calibrator auto.launch rviz:=true
```

2. **Manual RViz setup:**
   - Add PointCloud2 displays for base topic and `/calibrated/*` topics
   - Set fixed frame to base LiDAR frame
   - Use different colors for each LiDAR
   - Enable TF display to see coordinate frames

### Verification
- Calibrated point clouds should align with base LiDAR
- Static objects should appear as single, coherent structures
- Moving objects may show slight misalignment (normal)

## Troubleshooting

### Common Issues

1. **No convergence:**
   - Ensure sufficient scene overlap between LiDARs
   - Increase `gicp_max_iter` or `gicp_max_corr_dist`
   - Check for sufficient geometric features in the scene

2. **Time synchronization errors:**
   - Increase `time_tolerance`
   - Verify all LiDARs are publishing at similar rates
   - Check system clock synchronization

3. **Poor calibration quality:**
   - Reduce `voxel_leaf` for higher resolution
   - Ensure rich geometric environment (avoid empty spaces)
   - Consider motion deskewing for moving platforms

4. **Memory/performance issues:**
   - Increase `voxel_leaf` to reduce point density
   - Reduce `gicp_max_iter`
   - Limit number of simultaneous LiDARs

### Best Practices

1. **Environment:**
   - Use structured environments with geometric features
   - Avoid large empty spaces or repetitive patterns
   - Ensure good overlap between LiDAR fields of view

2. **Data Quality:**
   - Use motion deskewing if platform is moving
   - Ensure stable mounting of all sensors
   - Verify timestamp accuracy across sensors

3. **Parameter Tuning:**
   - Start with default parameters
   - Adjust `voxel_leaf` based on scene scale
   - Tune GICP parameters based on convergence behavior

## Advanced Usage

### Integration with Existing Systems
```bash
# Load saved calibration in another node
rosparam load ~/.ros/multi_lidar_extrinsics.yaml /my_namespace
```

### Batch Processing
```bash
# Save calibration periodically
while true; do
    sleep 60
    rosservice call /multi_lidar_calibrator/save_extrinsics
done
```

## Future Enhancements

- **Multi-frame pose graph optimization** for improved accuracy
- **Calibration target support** for structured environments
- **Motion deskewing integration** for mobile platforms
- **Automatic quality assessment** and convergence detection
- **GUI interface** for real-time monitoring and parameter tuning

## License

MIT License - see LICENSE file for details.

## Example Workflows

### Basic Calibration Workflow
```bash
# 1. Start your LiDAR drivers
roslaunch your_lidar_driver lidar_base.launch
roslaunch your_lidar_driver lidar_front.launch
# ... other LiDARs

# 2. Launch calibrator
roslaunch multi_lidar_calibrator auto.launch

# 3. Wait for convergence messages in terminal
# Look for: "GICP converged for /lidar_front/points: fitness = 0.023456"

# 4. Save results
rosservice call /multi_lidar_calibrator/save_extrinsics

# 5. Verify in RViz
rosrun rviz rviz
# Add PointCloud2 displays for /lidar_base/points and /calibrated/* topics
```

### Continuous Refinement Mode
```bash
# Enable continuous refinement for dynamic environments
roslaunch multi_lidar_calibrator auto.launch continuous_refine:=true

# Monitor fitness scores to ensure stability
rostopic echo /rosout | grep "fitness"
```

### Manual Topic Specification
```bash
# For systems with many sensors, specify only the ones you want to calibrate
roslaunch multi_lidar_calibrator auto.launch \
    auto_discover:=false \
    topics:="/velodyne_front/points /velodyne_rear/points /ouster_left/points"
```

## Performance Notes

- **Typical convergence time**: 5-30 seconds depending on scene complexity
- **Memory usage**: ~100-500MB per LiDAR depending on point density
- **CPU usage**: Moderate during calibration, low during steady-state
- **Recommended point cloud rate**: 5-20 Hz for real-time performance

## Validation

### Quantitative Metrics
- **Fitness Score**: Lower values indicate better alignment (typical: 0.01-0.1)
- **Convergence**: GICP should converge within specified iterations
- **Temporal Consistency**: Results should be stable across multiple frames

### Qualitative Assessment
- Visual inspection in RViz shows aligned point clouds
- Static objects appear as single coherent structures
- Geometric features align across different LiDAR views

## Contributing

Contributions welcome! Please submit issues and pull requests on the project repository.

## Support

For questions and support:
1. Check the troubleshooting section above
2. Review ROS logs for error messages
3. Verify your environment meets the requirements
4. Submit issues with detailed logs and configuration
