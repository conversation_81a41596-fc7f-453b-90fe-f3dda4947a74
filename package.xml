<?xml version="1.0"?>
<package format="2">
  <name>multi_lidar_calibrator</name>
  <version>1.0.0</version>
  <description>Multi-LiDAR extrinsic calibration package using GICP for targetless calibration</description>

  <maintainer email="<EMAIL>">Multi-LiDAR Calibrator Developer</maintainer>
  <license>MIT</license>

  <buildtool_depend>catkin</buildtool_depend>
  
  <!-- Core ROS dependencies -->
  <depend>roscpp</depend>
  <depend>sensor_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>std_srvs</depend>
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  
  <!-- PCL dependencies -->
  <depend>pcl_ros</depend>
  <depend>pcl_conversions</depend>
  
  <!-- System dependencies -->
  <build_depend>libpcl-all-dev</build_depend>
  <exec_depend>libpcl-all</exec_depend>

  <export>
  </export>
</package>
