# Multi-LiDAR Calibrator Configuration Parameters
# This file contains default parameters for the multi-LiDAR calibration system

# Base LiDAR configuration
base_topic: "/lidar_base/points"        # Topic of the base (reference) LiDAR
base_frame: ""                          # Base frame (empty = use message frame_id)

# Topic discovery
auto_discover: true                     # Automatically discover LiDAR topics
topics: ""                              # Space-separated list of other LiDAR topics (used if auto_discover is false)

# Time synchronization
time_tolerance: 0.05                    # Maximum time difference for frame pairing (seconds)

# Point cloud preprocessing
voxel_leaf: 0.1                         # Voxel filter leaf size (meters)

# GICP registration parameters
gicp_max_corr_dist: 1.0                 # Maximum correspondence distance for GICP
gicp_max_iter: 40                       # Maximum iterations for GICP
gicp_transformation_epsilon: 1e-6       # Transformation epsilon (convergence criterion)
gicp_euclidean_fitness_epsilon: 1e-6    # Euclidean fitness epsilon (convergence criterion)

# Calibration behavior
continuous_refine: false                # Enable continuous refinement of calibration
publish_tf_static: true                 # Publish static TF transforms

# Output configuration
save_yaml_path: "~/.ros/multi_lidar_extrinsics.yaml"  # Path to save calibration results

# Advanced parameters (optional)
# min_points_threshold: 100             # Minimum points required for calibration
# max_fitness_score: 0.5                # Maximum acceptable fitness score
# convergence_timeout: 30.0             # Timeout for initial convergence (seconds)
