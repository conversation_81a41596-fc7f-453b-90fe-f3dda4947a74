<?xml version="1.0"?>
<launch>
    <!-- Multi-LiDAR Calibrator Launch File -->
    
    <!-- Arguments -->
    <arg name="base_topic" default="/lidar_base/points" doc="Base LiDAR topic"/>
    <arg name="base_frame" default="" doc="Base frame (empty = use message frame_id)"/>
    <arg name="auto_discover" default="true" doc="Auto-discover LiDAR topics"/>
    <arg name="topics" default="" doc="Space-separated list of other LiDAR topics"/>
    <arg name="time_tolerance" default="0.05" doc="Time synchronization tolerance (seconds)"/>
    <arg name="voxel_leaf" default="0.1" doc="Voxel filter leaf size (meters)"/>
    <arg name="gicp_max_corr_dist" default="1.0" doc="GICP max correspondence distance"/>
    <arg name="gicp_max_iter" default="40" doc="GICP maximum iterations"/>
    <arg name="continuous_refine" default="false" doc="Enable continuous refinement"/>
    <arg name="publish_tf_static" default="true" doc="Publish static TF transforms"/>
    <arg name="save_yaml_path" default="~/.ros/multi_lidar_extrinsics.yaml" doc="YAML save path"/>
    <arg name="config_file" default="$(find multi_lidar_calibrator)/config/params.yaml" doc="Config file path"/>
    
    <!-- Load parameters from config file -->
    <rosparam file="$(arg config_file)" command="load" ns="multi_lidar_calibrator"/>
    
    <!-- Multi-LiDAR Calibrator Node -->
    <node name="multi_lidar_calibrator" pkg="multi_lidar_calibrator" type="multi_lidar_calibrator_node" output="screen">
        <!-- Override parameters with launch arguments -->
        <param name="base_topic" value="$(arg base_topic)"/>
        <param name="base_frame" value="$(arg base_frame)"/>
        <param name="auto_discover" value="$(arg auto_discover)"/>
        <param name="topics" value="$(arg topics)"/>
        <param name="time_tolerance" value="$(arg time_tolerance)"/>
        <param name="voxel_leaf" value="$(arg voxel_leaf)"/>
        <param name="gicp_max_corr_dist" value="$(arg gicp_max_corr_dist)"/>
        <param name="gicp_max_iter" value="$(arg gicp_max_iter)"/>
        <param name="continuous_refine" value="$(arg continuous_refine)"/>
        <param name="publish_tf_static" value="$(arg publish_tf_static)"/>
        <param name="save_yaml_path" value="$(arg save_yaml_path)"/>
    </node>
    
    <!-- Optional: RViz for visualization -->
    <arg name="rviz" default="false" doc="Launch RViz for visualization"/>
    <node if="$(arg rviz)" name="rviz" pkg="rviz" type="rviz" 
          args="-d $(find multi_lidar_calibrator)/config/calibration.rviz" output="screen"/>
    
</launch>
