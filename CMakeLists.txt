cmake_minimum_required(VERSION 3.0.2)
project(multi_lidar_calibrator)

## Compile as C++17
add_compile_options(-std=c++17 -O3)

## Find catkin macros and libraries
find_package(catkin REQUIRED COMPONENTS
  roscpp
  sensor_msgs
  geometry_msgs
  std_srvs
  tf2
  tf2_ros
  pcl_ros
  pcl_conversions
)

## Find PCL
find_package(PCL REQUIRED COMPONENTS
  common
  io
  filters
  registration
)

## System dependencies
find_package(Boost REQUIRED COMPONENTS system)

###################################
## catkin specific configuration ##
###################################
catkin_package(
  INCLUDE_DIRS include
  LIBRARIES ${PROJECT_NAME}
  CATKIN_DEPENDS 
    roscpp 
    sensor_msgs 
    geometry_msgs 
    std_srvs 
    tf2 
    tf2_ros 
    pcl_ros 
    pcl_conversions
  DEPENDS 
    PCL
    Boost
)

###########
## Build ##
###########

## Specify additional locations of header files
include_directories(
  include
  ${catkin_INCLUDE_DIRS}
  ${PCL_INCLUDE_DIRS}
  ${Boost_INCLUDE_DIRS}
)

## Add PCL definitions
add_definitions(${PCL_DEFINITIONS})

## Declare a C++ executable
add_executable(${PROJECT_NAME}_node src/multi_lidar_calibrator.cpp)

## Add cmake target dependencies of the executable
add_dependencies(${PROJECT_NAME}_node ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})

## Specify libraries to link a library or executable target against
target_link_libraries(${PROJECT_NAME}_node
  ${catkin_LIBRARIES}
  ${PCL_LIBRARIES}
  ${Boost_LIBRARIES}
)

#############
## Install ##
#############

## Mark executables for installation
install(TARGETS ${PROJECT_NAME}_node
  RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

## Mark launch files for installation
install(DIRECTORY launch/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/launch
  PATTERN ".svn" EXCLUDE
)

## Mark config files for installation
install(DIRECTORY config/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/config
  PATTERN ".svn" EXCLUDE
)

#############
## Testing ##
#############

## Add gtest based cpp tests
if(CATKIN_ENABLE_TESTING)
  find_package(rostest REQUIRED)
  # add_rostest_gtest(${PROJECT_NAME}-test test/test_multi_lidar_calibrator.launch test/test_multi_lidar_calibrator.cpp)
  # if(TARGET ${PROJECT_NAME}-test)
  #   target_link_libraries(${PROJECT_NAME}-test ${PROJECT_NAME})
  # endif()
endif()
